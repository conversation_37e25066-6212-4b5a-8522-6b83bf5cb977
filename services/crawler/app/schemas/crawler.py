from pydantic import BaseModel
from typing import List, Optional


class CrawlRequest(BaseModel):
    """Schema for crawl request payload."""
    url: str
    max_pages: int = 5


class CrawledDataResponse(BaseModel):
    """Schema for crawled data response."""
    id: int
    url: str
    title: Optional[str] = None
    clean_text: Optional[str] = None
    status_code: Optional[int] = None

    class Config:
        from_attributes = True


class PageLinkResponse(BaseModel):
    """Schema for page link response."""
    id: int
    url: str
    text: Optional[str] = None
    title: Optional[str] = None
    context: Optional[str] = None
    link_type: Optional[str] = None

    class Config:
        from_attributes = True


class CrawledDataWithLinks(CrawledDataResponse):
    """Schema for crawled data response including associated links."""
    links: List[PageLinkResponse] = []

    class Config:
        from_attributes = True
