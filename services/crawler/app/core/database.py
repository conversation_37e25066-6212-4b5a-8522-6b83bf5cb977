import os
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from models.database import Base


class DatabaseManager:
    """Database connection and management class."""
    
    def __init__(self, database_url: str = None):
        self.database_url = database_url or os.getenv(
            "DATABASE_URL", 
            "********************************************/crawl_db"
        )
        self.engine = create_engine(self.database_url)
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
    
    def init_db(self):
        """Create database tables."""
        Base.metadata.create_all(bind=self.engine)
    
    def migrate_db(self):
        """Add new columns to existing tables if they don't exist."""
        try:
            with self.engine.connect() as conn:
                # Check if new columns exist and add them if they don't
                
                # Add new columns to crawled_data table
                try:
                    conn.execute(text("ALTER TABLE crawled_data ADD COLUMN markdown_content TEXT"))
                except Exception:
                    pass  # Column already exists
                
                try:
                    conn.execute(text("ALTER TABLE crawled_data ADD COLUMN clean_text TEXT"))
                except Exception:
                    pass  # Column already exists
                
                try:
                    conn.execute(text("ALTER TABLE crawled_data ADD COLUMN page_metadata TEXT"))
                except Exception:
                    pass  # Column already exists
                
                try:
                    conn.execute(text("ALTER TABLE crawled_data ADD COLUMN status_code INTEGER"))
                except Exception:
                    pass  # Column already exists
                
                # Add new columns to page_links table
                try:
                    conn.execute(text("ALTER TABLE page_links ADD COLUMN title VARCHAR"))
                except Exception:
                    pass  # Column already exists
                
                try:
                    conn.execute(text("ALTER TABLE page_links ADD COLUMN context TEXT"))
                except Exception:
                    pass  # Column already exists
                
                try:
                    conn.execute(text("ALTER TABLE page_links ADD COLUMN link_type VARCHAR"))
                except Exception:
                    pass  # Column already exists
                
                conn.commit()
        except Exception as e:
            print(f"Migration warning: {e}")
    
    def get_session(self):
        """Get a database session."""
        return self.SessionLocal()


# Global database manager instance
db_manager = DatabaseManager()
