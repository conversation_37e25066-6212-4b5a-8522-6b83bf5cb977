import os
from pydantic import BaseModel


class Settings(BaseModel):
    """Application settings and configuration."""
    
    # Database settings
    database_url: str = os.getenv(
        "DATABASE_URL", 
        "********************************************/crawl_db"
    )
    
    # Application settings
    app_title: str = "Web Crawler Service"
    app_version: str = "1.0.0"
    debug: bool = os.getenv("DEBUG", "false").lower() == "true"
    
    # Server settings
    host: str = os.getenv("HOST", "0.0.0.0")
    port: int = int(os.getenv("PORT", "8000"))
    
    # Crawler settings
    default_max_pages: int = 5
    word_count_threshold: int = 1
    crawler_verbose: bool = True
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# Global settings instance
settings = Settings()
