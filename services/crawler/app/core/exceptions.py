from fastapi import HTTPException
from typing import Any, Dict, Optional


class CrawlerException(Exception):
    """Base exception class for crawler-related errors."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        self.message = message
        self.details = details or {}
        super().__init__(self.message)


class CrawlFailedException(CrawlerException):
    """Exception raised when crawling a URL fails."""
    pass


class DatabaseException(CrawlerException):
    """Exception raised for database-related errors."""
    pass


class ValidationException(CrawlerException):
    """Exception raised for validation errors."""
    pass


def create_http_exception(
    status_code: int,
    message: str,
    details: Optional[Dict[str, Any]] = None
) -> HTTPException:
    """
    Create an HTTPException with optional details.
    
    Args:
        status_code: HTTP status code
        message: Error message
        details: Optional additional details
        
    Returns:
        HTTPException: Formatted HTTP exception
    """
    detail = {"message": message}
    if details:
        detail.update(details)
    
    return HTTPException(status_code=status_code, detail=detail)
