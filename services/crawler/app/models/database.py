from sqlalchemy import Column, Integer, String, Text, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship

Base = declarative_base()


class CrawledData(Base):
    """Database model for crawled web page data."""
    __tablename__ = "crawled_data"

    id = Column(Integer, primary_key=True, index=True)
    url = Column(String, index=True, unique=True)
    title = Column(String, nullable=True)
    content = Column(Text, nullable=True)  # Raw HTML content
    markdown_content = Column(Text, nullable=True)  # Clean markdown content
    clean_text = Column(Text, nullable=True)  # Text content without HTML tags
    page_metadata = Column(Text, nullable=True)  # JSON metadata (description, author, etc.)
    status_code = Column(Integer, nullable=True)  # HTTP status code
    links = relationship("PageLink", back_populates="page")


class PageLink(Base):
    """Database model for links found on crawled pages."""
    __tablename__ = "page_links"

    id = Column(Integer, primary_key=True, index=True)
    url = Column(String, index=True)
    text = Column(String, nullable=True)
    title = Column(String, nullable=True)  # Link title attribute
    context = Column(Text, nullable=True)  # Surrounding text context
    link_type = Column(String, nullable=True)  # internal/external
    page_id = Column(Integer, ForeignKey('crawled_data.id'))

    page = relationship("CrawledData", back_populates="links")
