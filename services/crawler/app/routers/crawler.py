from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.orm import Session
from typing import List

from app.schemas.crawler import CrawlRequest, CrawledDataResponse, CrawledDataWithLinks
from app.services.crawler import CrawlerService
from app.dependencies import get_db

router = APIRouter(
    prefix="/api/v1",
    tags=["crawler"]
)

# Initialize crawler service
crawler_service = CrawlerService()


@router.post("/crawl/", response_model=CrawledDataWithLinks)
async def crawl_website(crawl_request: CrawlRequest, db: Session = Depends(get_db)):
    """
    Crawl a single webpage using Crawl4AI, extract comprehensive content and links,
    and store the results in the database.

    Args:
        crawl_request: The crawl request containing URL and max_pages (for backward compatibility)
        db: Database session dependency

    Returns:
        CrawledDataWithLinks: The crawled data including associated links
    """
    try:
        result = await crawler_service.crawl_url(crawl_request, db)
        return result
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/crawled-data/", response_model=List[CrawledDataResponse])
async def get_crawled_data(skip: int = 0, limit: int = 10, db: Session = Depends(get_db)):
    """
    Retrieve crawled data from the database.
    
    Args:
        skip: Number of records to skip (for pagination)
        limit: Maximum number of records to return
        db: Database session dependency
        
    Returns:
        List[CrawledDataResponse]: List of crawled data records
    """
    try:
        return crawler_service.get_crawled_data(db, skip=skip, limit=limit)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
