from fastapi import FastAPI

from core.config import settings
from core.database import db_manager
from routers import crawler

# FastAPI app
app = FastAPI(
    title=settings.app_title,
    version=settings.app_version,
    debug=settings.debug
)

# Include routers
app.include_router(crawler.router)


# Initialize database on startup
@app.on_event("startup")
async def startup_event():
    db_manager.init_db()
    db_manager.migrate_db()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host=settings.host, port=settings.port)
