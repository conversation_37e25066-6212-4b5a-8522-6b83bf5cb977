import json
import re
from typing import Dict, Any, Optional
from sqlalchemy.orm import Session
from crawl4ai import AsyncWebCrawler, CacheMode
from bs4 import BeautifulSoup

from app.models.database import CrawledData, PageLink
from app.schemas.crawler import CrawlRequest, CrawledDataWithLinks
from app.core.config import settings


class CrawlerService:
    """Service class for web crawling operations."""
    
    def __init__(self):
        self.word_count_threshold = settings.word_count_threshold
        self.verbose = settings.crawler_verbose
    
    async def crawl_url(self, crawl_request: CrawlRequest, db: Session) -> Dict[str, Any]:
        """
        Crawl a single webpage using Crawl4AI, extract comprehensive content and links,
        and store the results in the database.
        
        Args:
            crawl_request: The crawl request containing URL and options
            db: Database session
            
        Returns:
            Dictionary containing crawled data and links
        """
        # Check if URL already exists
        existing_page = db.query(CrawledData).filter(
            CrawledData.url == crawl_request.url
        ).first()
        
        if existing_page:
            return self._build_response_data(existing_page, db)
        
        # Use Crawl4AI to fetch and process the page
        async with AsyncWebCrawler() as crawler:
            result = await crawler.arun(
                url=crawl_request.url,
                cache_mode=CacheMode.BYPASS,
                word_count_threshold=self.word_count_threshold,
                verbose=self.verbose
            )
            
            if not result.success:
                raise Exception(f"Failed to crawl URL: {crawl_request.url}. Error: {result.error_message}")
            
            # Process the crawled data
            processed_data = self._process_crawl_result(result)
            
            # Save to database
            crawled_data = self._save_to_database(processed_data, result, db)
            
            return self._build_response_data(crawled_data, db)
    
    def _process_crawl_result(self, result) -> Dict[str, Any]:
        """Process the crawl result and extract relevant data."""
        # Extract metadata
        metadata_dict = result.metadata if result.metadata else {}
        
        # Get clean text from markdown
        clean_text = ""
        markdown_content = ""
        
        if result.markdown:
            if hasattr(result.markdown, 'raw_markdown'):
                markdown_content = result.markdown.raw_markdown
                # Convert markdown to plain text (simple approach)
                clean_text = re.sub(r'[#*`\[\]()_~]', '', markdown_content)
                clean_text = re.sub(r'\n+', '\n', clean_text).strip()
            else:
                markdown_content = str(result.markdown)
                clean_text = markdown_content
        
        # Extract title from metadata or HTML
        title = metadata_dict.get('title', 'No title')
        if not title or title == 'No title':
            title = self._extract_title_from_html(result.html)
        
        return {
            'title': title,
            'markdown_content': markdown_content,
            'clean_text': clean_text,
            'metadata_dict': metadata_dict
        }
    
    def _extract_title_from_html(self, html: str) -> str:
        """Extract title from HTML content."""
        try:
            soup = BeautifulSoup(html, 'html.parser')
            title_tag = soup.find('title')
            if title_tag:
                return title_tag.get_text().strip()
        except Exception:
            pass
        return "No title"
    
    def _save_to_database(self, processed_data: Dict[str, Any], result, db: Session) -> CrawledData:
        """Save crawled data to database."""
        # Save page to database
        data = CrawledData(
            url=result.url,
            title=processed_data['title'],
            content=result.html,
            markdown_content=processed_data['markdown_content'],
            clean_text=processed_data['clean_text'],
            page_metadata=json.dumps(processed_data['metadata_dict']) if processed_data['metadata_dict'] else None,
            status_code=result.status_code
        )
        db.add(data)
        db.flush()  # Flush to get the ID for the relationship
        
        # Extract and save links
        self._save_links(result, data.id, db)
        
        db.commit()
        return data
    
    def _save_links(self, result, page_id: int, db: Session):
        """Save extracted links to database."""
        # Process internal links
        if result.links and "internal" in result.links:
            for link in result.links["internal"]:
                page_link = PageLink(
                    url=link.get("href", ""),
                    text=link.get("text", "").strip(),
                    title=link.get("title", ""),
                    context=link.get("context", ""),
                    link_type="internal",
                    page_id=page_id
                )
                db.add(page_link)
        
        # Process external links
        if result.links and "external" in result.links:
            for link in result.links["external"]:
                page_link = PageLink(
                    url=link.get("href", ""),
                    text=link.get("text", "").strip(),
                    title=link.get("title", ""),
                    context=link.get("context", ""),
                    link_type="external",
                    page_id=page_id
                )
                db.add(page_link)
    
    def _build_response_data(self, crawled_data: CrawledData, db: Session) -> Dict[str, Any]:
        """Build response data from crawled data and links."""
        # Get the links for the response
        links = db.query(PageLink).filter(PageLink.page_id == crawled_data.id).all()
        
        return {
            "id": crawled_data.id,
            "url": crawled_data.url,
            "title": crawled_data.title,
            "clean_text": crawled_data.clean_text,
            "status_code": crawled_data.status_code,
            "links": [{
                "id": link.id,
                "url": link.url,
                "text": link.text,
                "title": link.title,
                "context": link.context,
                "link_type": link.link_type
            } for link in links]
        }
    
    def get_crawled_data(self, db: Session, skip: int = 0, limit: int = 10):
        """Retrieve crawled data from the database."""
        return db.query(CrawledData).offset(skip).limit(limit).all()
