
services:
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: crawl_db
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  crawler:
    build:
      context: ./services/crawler
      dockerfile: Dockerfile
    volumes:
      - ./services/crawler:/app
    environment:
      - DATABASE_URL=********************************************/crawl_db
      - DEBUG=true
    depends_on:
      postgres:
        condition: service_healthy
    command: ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
    ports:
      - "8000:8000"

volumes:
  postgres_data:
